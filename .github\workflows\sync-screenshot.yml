name: Sync Screenshot
on:
  workflow_dispatch

concurrency:
  group: "manual-sync-screenshot"
  cancel-in-progress: false

jobs:
  e2e:
    # can not update screenshot run on main.
    if: github.ref_name != 'main'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Set Git user.name and user.email from trigger actor
        run: |
          git config --global user.name "${{ github.actor }}"
          git config --global user.email "${{ github.actor }}@users.noreply.github.com"

      - name: Rush Install
        run: node common/scripts/install-run-rush.js install

      - name: Rush build
        run: node common/scripts/install-run-rush.js build

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps

      - name: Run E2E tests
        run: node common/scripts/install-run-rush.js e2e:update-screenshot --verbose

      - name: Commit and push changes
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git add .
          if git diff-index --quiet HEAD; then
            echo "No changes to commit"
          else
            git commit -m "chore: sync screenshot"
            git push https://x-access-token:${GH_TOKEN}@github.com/${{ github.repository }} HEAD:${{ github.ref_name }}
          fi
