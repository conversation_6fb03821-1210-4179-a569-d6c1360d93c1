{"name": "@flowgram.ai/demo-node-form", "version": "0.1.0", "description": "", "keywords": [], "license": "MIT", "main": "./src/index.tsx", "files": ["src/", ".eslintrc.js", ".giti<PERSON>re", "index.html", "package.json", "rsbuild.config.ts", "tsconfig.json"], "scripts": {"build": "exit 0", "build:fast": "exit 0", "build:watch": "exit 0", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "cross-env MODE=app NODE_ENV=development rsbuild dev --open", "lint": "eslint ./src --cache", "lint:fix": "eslint ./src --fix", "start": "cross-env NODE_ENV=development rsbuild dev --open", "test": "exit", "test:cov": "exit", "watch": "exit 0"}, "dependencies": {"@douyinfe/semi-icons": "^2.80.0", "@douyinfe/semi-ui": "^2.80.0", "@flowgram.ai/free-layout-editor": "workspace:*", "@flowgram.ai/free-snap-plugin": "workspace:*", "@flowgram.ai/minimap-plugin": "workspace:*", "react": "^18", "react-dom": "^18", "styled-components": "^5"}, "devDependencies": {"@flowgram.ai/ts-config": "workspace:*", "@flowgram.ai/eslint-config": "workspace:*", "@rsbuild/core": "^1.2.16", "@rsbuild/plugin-react": "^1.1.1", "@types/lodash-es": "^4.17.12", "@types/node": "^18", "@types/react": "^18", "@types/react-dom": "^18", "@types/styled-components": "^5", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.54.0", "cross-env": "~7.0.3"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}