/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

.node-placeholder {
  width: 360px;

  background-color: rgba(252, 252, 255, 1);
  border: 1px solid rgba(68, 83, 130, 0.25);
  border-radius: 8px;
  box-shadow:
    0 4px 12px 0 rgba(0, 0, 0, 2%),
    0 2px 6px 0 rgba(0, 0, 0, 4%);
}

.node-placeholder-skeleton {
  width: 100%;
  padding: 12px;
  background-color: rgba(252, 252, 255, 1);
  border-radius: 8px;
}

.node-placeholder-hd {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.node-placeholder-avatar {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 6px;
}

.node-placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 3px;
}

.node-placeholder-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2.5px;
}
