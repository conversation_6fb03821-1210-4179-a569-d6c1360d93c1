name: Deploy With Actions
on:
  workflow_dispatch

concurrency:
  group: "main-deploy-branch-workflow" # 唯一标识符，确保只运行一个实例
  cancel-in-progress: false     # 不取消正在运行的实例，后续触发需要等待当前实例完成

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pages: write
      id-token: write
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - name: Check if branch is main
        run: |
          if [ "$GITHUB_REF" != "refs/heads/main" ]; then
            echo "Not on main branch, exiting workflow."
            exit 1
          fi
          echo "On main branch, continuing workflow."
      - name: Config Git User
        run: |
          git config --local user.name "dragooncjw"
          git config --local user.email "<EMAIL>"
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          registry-url: 'https://registry.npmjs.org/'
      - name: Rush Install
        run: node common/scripts/install-run-rush.js install
      - name: Rush build
        run: node common/scripts/install-run-rush.js build
      - name: Generate docs
        run: |
          cd apps/docs
          npm run docs
      - name: Copy auto-docs to en
        run: cp -r apps/docs/src/zh/auto-docs apps/docs/src/en/auto-docs
      - name: Build Doc site
        run: |
          cd apps/docs
          npm run build
      - name: Replace docs
        run: |
          rm -rf docs
          mv apps/docs/doc_build docs
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: docs
          name: github-pages

  deploy:
    permissions:
      contents: read
      pages: write
      id-token: write
    needs: build
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

