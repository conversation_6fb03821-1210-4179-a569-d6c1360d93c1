/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

:root {
  /* Port colors */
  --g-workflow-port-color-primary: #4d53e8;
  --g-workflow-port-color-secondary: #9197f1;
  --g-workflow-port-color-error: #ff0000;
  --g-workflow-port-color-background: #ffffff;

  /* Line colors */
  --g-workflow-line-color-hidden: transparent;
  --g-workflow-line-color-default: #4d53e8;
  --g-workflow-line-color-drawing: #5dd6e3;
  --g-workflow-line-color-hover: #37d0ff;
  --g-workflow-line-color-selected: #37d0ff;
  --g-workflow-line-color-error: red;
}

.gedit-selector-bounds-background {
  cursor: move;
  display: none !important;
}

.gedit-selector-bounds-foreground {
  cursor: move;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 0;
  outline: 1px solid var(--g-playground-selectBox-outline);
  z-index: 33;
  background-color: var(--g-playground-selectBox-background);
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.node-running {
  border: 1px dashed rgb(78, 64, 229) !important;
  border-radius: 8px;
}
.demo-editor {
  flex-grow: 1;
  position: relative;
  height: 100%;
}

.demo-container {
  position: absolute;
  left: 0px;
  top: 0px;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
}

.demo-tools {
  padding: 10px;
  display: flex;
  justify-content: space-between;
}

.demo-tools-group > * {
  margin-right: 8px;
}

.mouse-pad-option-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
