/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

export enum FlowCommandId {
  COPY = 'COPY',
  PASTE = 'PASTE',
  CUT = 'CUT',
  GROUP = 'GROUP',
  UNGROUP = 'UNGROUP',
  COLLAPSE = 'COLLPASE',
  EXPAND = 'EXPAND',
  DELETE = 'DELETE',
  ZOOM_IN = 'ZOOM_IN',
  ZOOM_OUT = 'ZOOM_OUT',
  RESET_ZOOM = 'RESET_ZOOM',
  SELECT_ALL = 'SELECT_ALL',
  CANCEL_SELECT = 'CANCEL_SELECT',
}
