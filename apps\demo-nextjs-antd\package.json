{"name": "@flowgram.ai/demo-nextjs-antd", "version": "0.1.0", "description": "", "keywords": [], "license": "MIT", "files": ["public/", "src/", ".eslintrc.js", ".giti<PERSON>re", "next.config.ts", "pnpm-lock.yaml", "postcss.config.mjs", "package.json", "tsconfig.json", "README.md"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint ./src --cache", "lint:fix": "eslint ./src --fix"}, "dependencies": {"antd": "^5.25.4", "react": "^18", "react-dom": "^18", "next": "15.2.4", "lodash-es": "^4.17.21", "classnames": "^2.5.1", "server-only": "^0.0.1", "styled-components": "^5", "nanoid": "^4.0.2", "@ant-design/icons": "5.x", "@flowgram.ai/free-layout-editor": "workspace:*", "@flowgram.ai/free-snap-plugin": "workspace:*", "@flowgram.ai/free-lines-plugin": "workspace:*", "@flowgram.ai/free-node-panel-plugin": "workspace:*", "@flowgram.ai/minimap-plugin": "workspace:*", "@flowgram.ai/free-container-plugin": "workspace:*", "@flowgram.ai/free-group-plugin": "workspace:*", "@flowgram.ai/form-antd-materials": "workspace:*"}, "devDependencies": {"@flowgram.ai/ts-config": "workspace:*", "@flowgram.ai/eslint-config": "workspace:*", "@types/styled-components": "^5", "typescript": "^5.0.4", "@types/lodash-es": "^4.17.12", "@types/node": "^18", "@types/next": "^9.0.0", "@types/react": "^18", "@types/react-dom": "^18", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^8.54.0", "@babel/eslint-parser": "~7.19.1", "eslint-plugin-json": "^4.0.1", "eslint-plugin-next": "0.0.0", "eslint-config-next": "^15.3.1", "@eslint/eslintrc": "^3", "sass": "^1.89.1"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}}