/**
 * Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
 * SPDX-License-Identifier: MIT
 */

module.exports = {
  parser: "@typescript-eslint/parser",
  parserOptions: {
    requireConfigFile: false,
    babelOptions: {
      babelrc: false,
      configFile: false,
      cwd: __dirname,
    },
  },
  ignorePatterns: [
    '**/*.d.ts',
    '**/__mocks__',
    '**/node_modules',
    '**/build',
    '**/dist',
    '**/es',
    '**/lib',
    '**/.codebase',
    '**/.changeset',
    '**/config',
    '**/common/scripts',
    '**/output',
    'error-log-str.js',
    '*.bundle.js',
    '*.min.js',
    '*.js.map',
    '**/output',
    '**/*.log',
    '**/tsconfig.tsbuildinfo',
    '**/vitest.config.ts',
    'package.json',
    '*.json',
  ],
  rules: {
    'no-console': 'off',
    'react/no-deprecated': 'off',
    'import/prefer-default-export': 'off',
    'lines-between-class-members': 'warn',
    'react/jsx-no-useless-fragment': 'off',
    'no-unused-vars': 'off',
    'no-redeclare': 'off',
    'no-empty-fuNction': 'off',
    'prefer-destructurin': 'off',
    'no-underscore-dangle': 'off',
    'no-empty-function': 'off',
    'no-multi-assign': 'off',
    'arrow-body-style': 'warn',
    'no-useless-constructor': 'off',
    'no-param-reassign': 'off',
    'max-classes-per-file': 'off',
    'grouped-accessor-pairs': 'off',
    'no-plusplus': 'off',
    'no-restricted-syntax': 'off',
    'react/destructuring-assignment': 'off',
    'import/extensions': 'off',
    'consistent-return': 'off',
    'jsx-a11y/no-static-element-interactions': 'off',
    'no-use-before-define': 'off',
    'no-bitwise': 'off',
    'no-case-declarations': 'off',
    'react/no-array-index-key': 'off',
    'react/require-default-props': 'off',
    'no-dupe-class-members': 'off',
    'react/jsx-props-no-spreading': 'off',
    'no-console': 'off',
    'no-shadow': 'off',
    'class-methods-use-this': 'off',
    'default-param-last': 'off',
    'no-unused-vars': 'off',
    'import/prefer-default-export': 'off',
    'import/extensions': 'off',
  },
}
